# Digital Library Platform Setup Guide

## Prerequisites

1. **Node.js** (v18 or higher)
2. **npm** or **yarn**
3. **Supabase Account** (free tier available)

## Supabase Setup

### 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up/login
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - Name: `digital-library`
   - Database Password: (choose a strong password)
   - Region: (choose closest to your users)
5. Click "Create new project"

### 2. Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://xxxxx.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)
   - **Service role key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

### 3. Configure Environment Variables

1. Update your `.env.local` file with your Supabase credentials:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-random-secret-key-here

# File Upload Configuration
MAX_FILE_SIZE=50000000  # 50MB in bytes
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,epub,mobi
```

### 4. Set Up Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the contents of `supabase/schema.sql`
3. Paste it into the SQL Editor and click "Run"

This will create:
- All necessary tables (profiles, documents, comments, ratings)
- Row Level Security policies
- Storage bucket for documents
- Triggers and functions

### 5. Configure Authentication

1. In Supabase dashboard, go to **Authentication** → **Settings**
2. Configure the following:

**Site URL**: `http://localhost:3000` (for development)

**Redirect URLs**: Add these URLs:
- `http://localhost:3000/auth/callback`
- `http://localhost:3000/dashboard`

**Email Templates**: Customize if needed (optional)

### 6. Set Up Storage

1. Go to **Storage** in your Supabase dashboard
2. The `documents` bucket should already be created by the schema
3. Verify the bucket exists and has the correct policies

## Local Development Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Run the Development Server

```bash
npm run dev
```

### 3. Access the Application

Open [http://localhost:3000](http://localhost:3000) in your browser.

## Initial Admin Setup

### 1. Create Your First User

1. Go to `/auth/register` and create an account
2. Check your email for the confirmation link
3. Click the confirmation link to activate your account

### 2. Make Yourself an Admin

1. In Supabase dashboard, go to **Table Editor** → **profiles**
2. Find your user record
3. Change the `role` field from `user` to `admin`
4. Save the changes

Now you'll have access to the admin panel at `/admin`.

## Features Overview

### For Users:
- **Browse Library**: Search and filter documents by subject, author, file type
- **Upload Documents**: Share your lecture notes, e-books, and materials
- **Preview Files**: View document previews before downloading
- **Comments & Ratings**: Engage with the community
- **Personal Dashboard**: Track your uploads and downloads

### For Admins:
- **Content Moderation**: Approve/reject uploaded documents
- **User Management**: Manage user accounts and roles
- **Analytics**: View platform statistics and activity
- **Content Management**: Edit or remove inappropriate content

## File Upload Limits

- **Maximum file size**: 50MB (configurable)
- **Allowed file types**: PDF, DOC, DOCX, TXT, EPUB, MOBI (configurable)
- **Storage**: Unlimited with Supabase (subject to plan limits)

## Security Features

- **Row Level Security**: Users can only access appropriate data
- **File Upload Validation**: Server-side validation of file types and sizes
- **Admin Moderation**: All uploads require admin approval
- **Authentication**: Secure user authentication with Supabase Auth

## Deployment

### Environment Variables for Production

Update your production environment variables:

```env
NEXT_PUBLIC_SUPABASE_URL=your-production-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-role-key
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-production-secret
```

### Supabase Production Settings

1. Update **Site URL** to your production domain
2. Add production **Redirect URLs**
3. Configure **Custom SMTP** for email delivery (optional)

## Troubleshooting

### Common Issues:

1. **Authentication not working**: Check environment variables and Supabase settings
2. **File uploads failing**: Verify storage bucket exists and has correct policies
3. **Database errors**: Ensure schema was applied correctly
4. **Permission errors**: Check Row Level Security policies

### Getting Help:

- Check the browser console for error messages
- Review Supabase logs in the dashboard
- Ensure all environment variables are set correctly

## Next Steps

1. Customize the UI/UX to match your branding
2. Add more file types if needed
3. Implement additional features like bookmarks, favorites, etc.
4. Set up monitoring and analytics
5. Configure backup strategies

## Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run linting
npm run lint
```
