import {
  formatFileSize,
  formatDate,
  formatRelativeTime,
  getFileIcon,
  validateFile,
  generateSlug,
  truncateText,
  isValidEmail,
  isValidPassword,
  debounce,
} from '@/lib/utils'

describe('Utils', () => {
  describe('formatFileSize', () => {
    it('should format bytes correctly', () => {
      expect(formatFileSize(0)).toBe('0 Bytes')
      expect(formatFileSize(1024)).toBe('1 KB')
      expect(formatFileSize(1048576)).toBe('1 MB')
      expect(formatFileSize(1073741824)).toBe('1 GB')
      expect(formatFileSize(1536)).toBe('1.5 KB')
    })
  })

  describe('formatDate', () => {
    it('should format date correctly', () => {
      const date = '2024-01-15T10:30:00Z'
      const formatted = formatDate(date)
      expect(formatted).toContain('January')
      expect(formatted).toContain('15')
      expect(formatted).toContain('2024')
    })
  })

  describe('formatRelativeTime', () => {
    it('should format relative time correctly', () => {
      const now = new Date()
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000)
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)

      expect(formatRelativeTime(oneMinuteAgo.toISOString())).toBe('1 minute ago')
      expect(formatRelativeTime(oneHourAgo.toISOString())).toBe('1 hour ago')
      expect(formatRelativeTime(oneDayAgo.toISOString())).toBe('1 day ago')
    })

    it('should return "just now" for very recent times', () => {
      const now = new Date()
      const thirtySecondsAgo = new Date(now.getTime() - 30 * 1000)
      expect(formatRelativeTime(thirtySecondsAgo.toISOString())).toBe('just now')
    })
  })

  describe('getFileIcon', () => {
    it('should return correct icons for file types', () => {
      expect(getFileIcon('application/pdf')).toBe('📄')
      expect(getFileIcon('application/msword')).toBe('📝')
      expect(getFileIcon('text/plain')).toBe('📃')
      expect(getFileIcon('application/epub+zip')).toBe('📚')
      expect(getFileIcon('unknown/type')).toBe('📄')
    })
  })

  describe('validateFile', () => {
    const createMockFile = (name: string, size: number, type: string) => {
      return new File([''], name, { type }) as File & { size: number }
    }

    beforeEach(() => {
      // Mock environment variables
      process.env.MAX_FILE_SIZE = '50000000'
      process.env.ALLOWED_FILE_TYPES = 'pdf,doc,docx,txt,epub,mobi'
    })

    it('should validate correct files', () => {
      const file = createMockFile('test.pdf', 1000000, 'application/pdf')
      file.size = 1000000
      const result = validateFile(file)
      expect(result.isValid).toBe(true)
    })

    it('should reject files that are too large', () => {
      const file = createMockFile('large.pdf', 60000000, 'application/pdf')
      file.size = 60000000
      const result = validateFile(file)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('File size must be less than')
    })

    it('should reject files with invalid extensions', () => {
      const file = createMockFile('test.exe', 1000, 'application/exe')
      file.size = 1000
      const result = validateFile(file)
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('File type not allowed')
    })
  })

  describe('generateSlug', () => {
    it('should generate correct slugs', () => {
      expect(generateSlug('Hello World')).toBe('hello-world')
      expect(generateSlug('Test@#$%^&*()File')).toBe('testfile')
      expect(generateSlug('Multiple   Spaces')).toBe('multiple-spaces')
      expect(generateSlug('Special-Characters!')).toBe('special-characters')
    })
  })

  describe('truncateText', () => {
    it('should truncate text correctly', () => {
      const longText = 'This is a very long text that should be truncated'
      expect(truncateText(longText, 20)).toBe('This is a very long...')
      expect(truncateText('Short text', 20)).toBe('Short text')
    })
  })

  describe('isValidEmail', () => {
    it('should validate emails correctly', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('invalid-email')).toBe(false)
      expect(isValidEmail('test@')).toBe(false)
      expect(isValidEmail('@example.com')).toBe(false)
    })
  })

  describe('isValidPassword', () => {
    it('should validate passwords correctly', () => {
      const validPassword = isValidPassword('Password123')
      expect(validPassword.isValid).toBe(true)
      expect(validPassword.errors).toHaveLength(0)

      const shortPassword = isValidPassword('Pass1')
      expect(shortPassword.isValid).toBe(false)
      expect(shortPassword.errors).toContain('Password must be at least 8 characters long')

      const noUppercase = isValidPassword('password123')
      expect(noUppercase.isValid).toBe(false)
      expect(noUppercase.errors).toContain('Password must contain at least one uppercase letter')

      const noLowercase = isValidPassword('PASSWORD123')
      expect(noLowercase.isValid).toBe(false)
      expect(noLowercase.errors).toContain('Password must contain at least one lowercase letter')

      const noNumber = isValidPassword('Password')
      expect(noNumber.isValid).toBe(false)
      expect(noNumber.errors).toContain('Password must contain at least one number')
    })
  })

  describe('debounce', () => {
    jest.useFakeTimers()

    it('should debounce function calls', () => {
      const mockFn = jest.fn()
      const debouncedFn = debounce(mockFn, 100)

      debouncedFn('test1')
      debouncedFn('test2')
      debouncedFn('test3')

      expect(mockFn).not.toHaveBeenCalled()

      jest.advanceTimersByTime(100)

      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockFn).toHaveBeenCalledWith('test3')
    })

    afterEach(() => {
      jest.clearAllTimers()
    })
  })
})
