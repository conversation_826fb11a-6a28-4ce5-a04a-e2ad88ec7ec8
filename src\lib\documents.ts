import { supabase } from './supabase'

export interface Document {
  id: string
  title: string
  description: string | null
  subject: string
  author: string | null
  file_url: string
  file_name: string
  file_size: number
  file_type: string
  thumbnail_url: string | null
  upload_user_id: string
  download_count: number
  is_approved: boolean
  created_at: string
  updated_at: string
}

export interface DocumentWithProfile extends Document {
  profiles: {
    full_name: string | null
    email: string
  }
}

export interface SearchFilters {
  subject?: string
  author?: string
  fileType?: string
  sortBy?: 'created_at' | 'title' | 'download_count'
  sortOrder?: 'asc' | 'desc'
}

export async function uploadDocument(
  file: File,
  metadata: {
    title: string
    description?: string
    subject: string
    author?: string
  },
  userId: string
): Promise<Document> {
  // Upload file to Supabase Storage
  const fileExt = file.name.split('.').pop()
  const fileName = `${userId}/${Date.now()}.${fileExt}`
  
  const { data: uploadData, error: uploadError } = await supabase.storage
    .from('documents')
    .upload(fileName, file)

  if (uploadError) throw uploadError

  // Get public URL
  const { data: { publicUrl } } = supabase.storage
    .from('documents')
    .getPublicUrl(fileName)

  // Insert document record
  const { data, error } = await supabase
    .from('documents')
    .insert({
      title: metadata.title,
      description: metadata.description,
      subject: metadata.subject,
      author: metadata.author,
      file_url: publicUrl,
      file_name: file.name,
      file_size: file.size,
      file_type: file.type,
      upload_user_id: userId,
    })
    .select()
    .single()

  if (error) throw error
  return data
}

export async function getDocuments(
  filters: SearchFilters = {},
  page: number = 1,
  limit: number = 20
): Promise<{ documents: DocumentWithProfile[], total: number }> {
  let query = supabase
    .from('documents')
    .select(`
      *,
      profiles:upload_user_id (
        full_name,
        email
      )
    `, { count: 'exact' })
    .eq('is_approved', true)

  // Apply filters
  if (filters.subject) {
    query = query.eq('subject', filters.subject)
  }
  
  if (filters.author) {
    query = query.ilike('author', `%${filters.author}%`)
  }
  
  if (filters.fileType) {
    query = query.eq('file_type', filters.fileType)
  }

  // Apply sorting
  const sortBy = filters.sortBy || 'created_at'
  const sortOrder = filters.sortOrder || 'desc'
  query = query.order(sortBy, { ascending: sortOrder === 'asc' })

  // Apply pagination
  const from = (page - 1) * limit
  const to = from + limit - 1
  query = query.range(from, to)

  const { data, error, count } = await query

  if (error) throw error

  return {
    documents: data || [],
    total: count || 0
  }
}

export async function searchDocuments(
  searchTerm: string,
  filters: SearchFilters = {},
  page: number = 1,
  limit: number = 20
): Promise<{ documents: DocumentWithProfile[], total: number }> {
  let query = supabase
    .from('documents')
    .select(`
      *,
      profiles:upload_user_id (
        full_name,
        email
      )
    `, { count: 'exact' })
    .eq('is_approved', true)
    .textSearch('title,description,subject,author', searchTerm)

  // Apply additional filters
  if (filters.subject) {
    query = query.eq('subject', filters.subject)
  }
  
  if (filters.fileType) {
    query = query.eq('file_type', filters.fileType)
  }

  // Apply sorting
  const sortBy = filters.sortBy || 'created_at'
  const sortOrder = filters.sortOrder || 'desc'
  query = query.order(sortBy, { ascending: sortOrder === 'asc' })

  // Apply pagination
  const from = (page - 1) * limit
  const to = from + limit - 1
  query = query.range(from, to)

  const { data, error, count } = await query

  if (error) throw error

  return {
    documents: data || [],
    total: count || 0
  }
}

export async function getDocumentById(id: string): Promise<DocumentWithProfile | null> {
  const { data, error } = await supabase
    .from('documents')
    .select(`
      *,
      profiles:upload_user_id (
        full_name,
        email
      )
    `)
    .eq('id', id)
    .eq('is_approved', true)
    .single()

  if (error) {
    console.error('Error fetching document:', error)
    return null
  }

  return data
}

export async function incrementDownloadCount(documentId: string): Promise<void> {
  const { error } = await supabase.rpc('increment_download_count', {
    document_id: documentId
  })

  if (error) {
    console.error('Error incrementing download count:', error)
  }
}

export async function getUserDocuments(
  userId: string,
  page: number = 1,
  limit: number = 20
): Promise<{ documents: Document[], total: number }> {
  const from = (page - 1) * limit
  const to = from + limit - 1

  const { data, error, count } = await supabase
    .from('documents')
    .select('*', { count: 'exact' })
    .eq('upload_user_id', userId)
    .order('created_at', { ascending: false })
    .range(from, to)

  if (error) throw error

  return {
    documents: data || [],
    total: count || 0
  }
}

export async function deleteDocument(documentId: string, userId: string): Promise<void> {
  // First get the document to check ownership and get file path
  const { data: document, error: fetchError } = await supabase
    .from('documents')
    .select('file_url, upload_user_id')
    .eq('id', documentId)
    .single()

  if (fetchError) throw fetchError

  if (document.upload_user_id !== userId) {
    throw new Error('Unauthorized: You can only delete your own documents')
  }

  // Extract file path from URL
  const url = new URL(document.file_url)
  const filePath = url.pathname.split('/').slice(-2).join('/')

  // Delete file from storage
  const { error: storageError } = await supabase.storage
    .from('documents')
    .remove([filePath])

  if (storageError) {
    console.error('Error deleting file from storage:', storageError)
  }

  // Delete document record
  const { error: deleteError } = await supabase
    .from('documents')
    .delete()
    .eq('id', documentId)

  if (deleteError) throw deleteError
}

export async function getSubjects(): Promise<string[]> {
  const { data, error } = await supabase
    .from('documents')
    .select('subject')
    .eq('is_approved', true)

  if (error) throw error

  const subjects = [...new Set(data.map(doc => doc.subject))].sort()
  return subjects
}

export async function getFileTypes(): Promise<string[]> {
  const { data, error } = await supabase
    .from('documents')
    .select('file_type')
    .eq('is_approved', true)

  if (error) throw error

  const fileTypes = [...new Set(data.map(doc => doc.file_type))].sort()
  return fileTypes
}
