{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/mca/src/components/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navigation.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/mca/src/components/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navigation.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/mca/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport Navigation from \"@/components/Navigation\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n\n      {/* Hero Section */}\n      <div className=\"relative bg-white overflow-hidden\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32\">\n            <main className=\"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28\">\n              <div className=\"sm:text-center lg:text-left\">\n                <h1 className=\"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl\">\n                  <span className=\"block xl:inline\">Digital Library for</span>{' '}\n                  <span className=\"block text-indigo-600 xl:inline\">Academic Excellence</span>\n                </h1>\n                <p className=\"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0\">\n                  Share, discover, and access lecture notes, e-books, and academic materials.\n                  Our platform features powerful search, file preview, and collaborative discussions\n                  to enhance your learning experience.\n                </p>\n                <div className=\"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start\">\n                  <div className=\"rounded-md shadow\">\n                    <Link\n                      href=\"/browse\"\n                      className=\"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10\"\n                    >\n                      🔍 Browse Library\n                    </Link>\n                  </div>\n                  <div className=\"mt-3 sm:mt-0 sm:ml-3\">\n                    <Link\n                      href=\"/auth/register\"\n                      className=\"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 md:py-4 md:text-lg md:px-10\"\n                    >\n                      📚 Join Community\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </main>\n          </div>\n        </div>\n        <div className=\"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2\">\n          <div className=\"h-56 w-full bg-gradient-to-r from-indigo-500 to-purple-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center\">\n            <div className=\"text-white text-8xl\">📚</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Features Section */}\n      <div className=\"py-12 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"lg:text-center\">\n            <h2 className=\"text-base text-indigo-600 font-semibold tracking-wide uppercase\">Features</h2>\n            <p className=\"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl\">\n              Everything you need for academic success\n            </p>\n            <p className=\"mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto\">\n              Our platform provides comprehensive tools for sharing and discovering academic materials.\n            </p>\n          </div>\n\n          <div className=\"mt-10\">\n            <div className=\"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10\">\n              <div className=\"relative\">\n                <div className=\"absolute flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white text-2xl\">\n                  🔍\n                </div>\n                <p className=\"ml-16 text-lg leading-6 font-medium text-gray-900\">Advanced Search</p>\n                <p className=\"mt-2 ml-16 text-base text-gray-500\">\n                  Find materials by subject, author, title, or content with our powerful search engine.\n                </p>\n              </div>\n\n              <div className=\"relative\">\n                <div className=\"absolute flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white text-2xl\">\n                  👁️\n                </div>\n                <p className=\"ml-16 text-lg leading-6 font-medium text-gray-900\">File Preview</p>\n                <p className=\"mt-2 ml-16 text-base text-gray-500\">\n                  Preview documents before downloading to ensure they meet your needs.\n                </p>\n              </div>\n\n              <div className=\"relative\">\n                <div className=\"absolute flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white text-2xl\">\n                  💬\n                </div>\n                <p className=\"ml-16 text-lg leading-6 font-medium text-gray-900\">Discussion & Comments</p>\n                <p className=\"mt-2 ml-16 text-base text-gray-500\">\n                  Engage with the community through comments and discussions on materials.\n                </p>\n              </div>\n\n              <div className=\"relative\">\n                <div className=\"absolute flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white text-2xl\">\n                  ⚙️\n                </div>\n                <p className=\"ml-16 text-lg leading-6 font-medium text-gray-900\">Admin Moderation</p>\n                <p className=\"mt-2 ml-16 text-base text-gray-500\">\n                  Quality-controlled content with admin approval and moderation systems.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* CTA Section */}\n      <div className=\"bg-indigo-700\">\n        <div className=\"max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8\">\n          <h2 className=\"text-3xl font-extrabold text-white sm:text-4xl\">\n            <span className=\"block\">Ready to get started?</span>\n            <span className=\"block\">Join our academic community today.</span>\n          </h2>\n          <p className=\"mt-4 text-lg leading-6 text-indigo-200\">\n            Upload your materials, discover new resources, and connect with fellow learners.\n          </p>\n          <Link\n            href=\"/auth/register\"\n            className=\"mt-8 w-full inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50 sm:w-auto\"\n          >\n            Sign up for free\n          </Link>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <footer className=\"bg-white\">\n        <div className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 md:flex md:items-center md:justify-between lg:px-8\">\n          <div className=\"flex justify-center space-x-6 md:order-2\">\n            <p className=\"text-gray-400 text-sm\">\n              Built with Next.js and Supabase\n            </p>\n          </div>\n          <div className=\"mt-8 md:mt-0 md:order-1\">\n            <p className=\"text-center text-base text-gray-400\">\n              &copy; 2024 Digital Library. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,UAAU;;;;;0BAGX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CACd,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAkB;;;;;;gDAA2B;8DAC7D,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,8OAAC;4CAAE,WAAU;sDAAoG;;;;;;sDAKjH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;8DAIH,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAAsB;;;;;;;;;;;;;;;;;;;;;;0BAM3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkE;;;;;;8CAChF,8OAAC;oCAAE,WAAU;8CAAkF;;;;;;8CAG/F,8OAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;sCAKjE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAmG;;;;;;0DAGlH,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;kDAKpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAmG;;;;;;0DAGlH,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;kDAKpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAmG;;;;;;0DAGlH,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;kDAKpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAmG;;;;;;0DAGlH,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAQ;;;;;;8CACxB,8OAAC;oCAAK,WAAU;8CAAQ;;;;;;;;;;;;sCAE1B,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;sCAGtD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/D", "debugId": null}}]}