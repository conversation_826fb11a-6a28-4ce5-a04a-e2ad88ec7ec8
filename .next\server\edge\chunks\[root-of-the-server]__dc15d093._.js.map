{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { NextResponse, type NextRequest } from 'next/server'\n\nexport async function middleware(request: NextRequest) {\n  let response = NextResponse.next({\n    request: {\n      headers: request.headers,\n    },\n  })\n\n  const supabase = createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        get(name: string) {\n          return request.cookies.get(name)?.value\n        },\n        set(name: string, value: string, options: any) {\n          request.cookies.set({\n            name,\n            value,\n            ...options,\n          })\n          response = NextResponse.next({\n            request: {\n              headers: request.headers,\n            },\n          })\n          response.cookies.set({\n            name,\n            value,\n            ...options,\n          })\n        },\n        remove(name: string, options: any) {\n          request.cookies.set({\n            name,\n            value: '',\n            ...options,\n          })\n          response = NextResponse.next({\n            request: {\n              headers: request.headers,\n            },\n          })\n          response.cookies.set({\n            name,\n            value: '',\n            ...options,\n          })\n        },\n      },\n    }\n  )\n\n  const {\n    data: { user },\n  } = await supabase.auth.getUser()\n\n  // Protected routes that require authentication\n  const protectedRoutes = ['/dashboard', '/upload', '/profile', '/admin']\n  const adminRoutes = ['/admin']\n  \n  const isProtectedRoute = protectedRoutes.some(route => \n    request.nextUrl.pathname.startsWith(route)\n  )\n  const isAdminRoute = adminRoutes.some(route => \n    request.nextUrl.pathname.startsWith(route)\n  )\n\n  // Redirect to login if accessing protected route without authentication\n  if (isProtectedRoute && !user) {\n    const redirectUrl = new URL('/auth/login', request.url)\n    redirectUrl.searchParams.set('redirectTo', request.nextUrl.pathname)\n    return NextResponse.redirect(redirectUrl)\n  }\n\n  // Check admin access\n  if (isAdminRoute && user) {\n    const { data: profile } = await supabase\n      .from('profiles')\n      .select('role')\n      .eq('id', user.id)\n      .single()\n\n    if (!profile || profile.role !== 'admin') {\n      return NextResponse.redirect(new URL('/dashboard', request.url))\n    }\n  }\n\n  // Redirect authenticated users away from auth pages\n  if (user && request.nextUrl.pathname.startsWith('/auth/')) {\n    return NextResponse.redirect(new URL('/dashboard', request.url))\n  }\n\n  return response\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;;;AAEO,eAAe,WAAW,OAAoB;IACnD,IAAI,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC/B,SAAS;YACP,SAAS,QAAQ,OAAO;QAC1B;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,sUAGhC;QACE,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,OAAO;YACpC;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAY;gBAC3C,QAAQ,OAAO,CAAC,GAAG,CAAC;oBAClB;oBACA;oBACA,GAAG,OAAO;gBACZ;gBACA,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAC3B,SAAS;wBACP,SAAS,QAAQ,OAAO;oBAC1B;gBACF;gBACA,SAAS,OAAO,CAAC,GAAG,CAAC;oBACnB;oBACA;oBACA,GAAG,OAAO;gBACZ;YACF;YACA,QAAO,IAAY,EAAE,OAAY;gBAC/B,QAAQ,OAAO,CAAC,GAAG,CAAC;oBAClB;oBACA,OAAO;oBACP,GAAG,OAAO;gBACZ;gBACA,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAC3B,SAAS;wBACP,SAAS,QAAQ,OAAO;oBAC1B;gBACF;gBACA,SAAS,OAAO,CAAC,GAAG,CAAC;oBACnB;oBACA,OAAO;oBACP,GAAG,OAAO;gBACZ;YACF;QACF;IACF;IAGF,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,+CAA+C;IAC/C,MAAM,kBAAkB;QAAC;QAAc;QAAW;QAAY;KAAS;IACvE,MAAM,cAAc;QAAC;KAAS;IAE9B,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAEtC,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,QACpC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAGtC,wEAAwE;IACxE,IAAI,oBAAoB,CAAC,MAAM;QAC7B,MAAM,cAAc,IAAI,IAAI,eAAe,QAAQ,GAAG;QACtD,YAAY,YAAY,CAAC,GAAG,CAAC,cAAc,QAAQ,OAAO,CAAC,QAAQ;QACnE,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,qBAAqB;IACrB,IAAI,gBAAgB,MAAM;QACxB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,SAAS;YACxC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;QAChE;IACF;IAEA,oDAAoD;IACpD,IAAI,QAAQ,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;QACzD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;IAChE;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}