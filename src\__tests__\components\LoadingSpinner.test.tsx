import { render, screen } from '@testing-library/react'
import LoadingSpinner, { PageLoadingSpinner, InlineLoadingSpinner } from '@/components/LoadingSpinner'

describe('LoadingSpinner', () => {
  it('renders with default props', () => {
    render(<LoadingSpinner />)
    
    expect(screen.getByText('Loading...')).toBeInTheDocument()
    const spinner = screen.getByText('Loading...').previousElementSibling
    expect(spinner).toHaveClass('h-8', 'w-8', 'animate-spin')
  })

  it('renders with custom size', () => {
    render(<LoadingSpinner size="lg" />)
    
    const spinner = screen.getByText('Loading...').previousElementSibling
    expect(spinner).toHaveClass('h-12', 'w-12')
  })

  it('renders with custom text', () => {
    render(<LoadingSpinner text="Please wait..." />)
    
    expect(screen.getByText('Please wait...')).toBeInTheDocument()
  })

  it('renders without text when text is empty', () => {
    render(<LoadingSpinner text="" />)
    
    expect(screen.queryByText('Loading...')).not.toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(<LoadingSpinner className="custom-class" />)
    
    const container = screen.getByText('Loading...').parentElement
    expect(container).toHaveClass('custom-class')
  })
})

describe('PageLoadingSpinner', () => {
  it('renders with full screen layout', () => {
    render(<PageLoadingSpinner />)
    
    const container = screen.getByText('Loading...').closest('div')
    expect(container).toHaveClass('min-h-screen', 'flex', 'items-center', 'justify-center')
  })

  it('renders with custom text', () => {
    render(<PageLoadingSpinner text="Loading page..." />)
    
    expect(screen.getByText('Loading page...')).toBeInTheDocument()
  })
})

describe('InlineLoadingSpinner', () => {
  it('renders with inline layout', () => {
    render(<InlineLoadingSpinner />)
    
    const container = screen.getByText('Loading...').closest('div')
    expect(container).toHaveClass('flex', 'items-center', 'justify-center', 'py-8')
  })

  it('renders with custom text', () => {
    render(<InlineLoadingSpinner text="Loading content..." />)
    
    expect(screen.getByText('Loading content...')).toBeInTheDocument()
  })
})
