{"version": 3, "sources": [], "sections": [{"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/mca/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)\n\n// Server-side Supabase client with service role key\nexport const supabaseAdmin = createClient(\n  supabaseUrl,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!,\n  {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  }\n)\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string | null\n          avatar_url: string | null\n          role: 'user' | 'admin'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name?: string | null\n          avatar_url?: string | null\n          role?: 'user' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          full_name?: string | null\n          avatar_url?: string | null\n          role?: 'user' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      documents: {\n        Row: {\n          id: string\n          title: string\n          description: string | null\n          subject: string\n          author: string | null\n          file_url: string\n          file_name: string\n          file_size: number\n          file_type: string\n          thumbnail_url: string | null\n          upload_user_id: string\n          download_count: number\n          is_approved: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          title: string\n          description?: string | null\n          subject: string\n          author?: string | null\n          file_url: string\n          file_name: string\n          file_size: number\n          file_type: string\n          thumbnail_url?: string | null\n          upload_user_id: string\n          download_count?: number\n          is_approved?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          title?: string\n          description?: string | null\n          subject?: string\n          author?: string | null\n          file_url?: string\n          file_name?: string\n          file_size?: number\n          file_type?: string\n          thumbnail_url?: string | null\n          upload_user_id?: string\n          download_count?: number\n          is_approved?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      comments: {\n        Row: {\n          id: string\n          document_id: string\n          user_id: string\n          content: string\n          parent_id: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          document_id: string\n          user_id: string\n          content: string\n          parent_id?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          document_id?: string\n          user_id?: string\n          content?: string\n          parent_id?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      ratings: {\n        Row: {\n          id: string\n          document_id: string\n          user_id: string\n          rating: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          document_id: string\n          user_id: string\n          rating: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          document_id?: string\n          user_id?: string\n          rating?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAGlD,MAAM,gBAAgB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACtC,aACA,QAAQ,GAAG,CAAC,yBAAyB,EACrC;IACE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/mca/src/lib/auth.ts"], "sourcesContent": ["import { supabase } from './supabase'\nimport { User } from '@supabase/supabase-js'\n\nexport interface UserProfile {\n  id: string\n  email: string\n  full_name: string | null\n  avatar_url: string | null\n  role: 'user' | 'admin'\n  created_at: string\n  updated_at: string\n}\n\nexport async function signUp(email: string, password: string, fullName: string) {\n  const { data, error } = await supabase.auth.signUp({\n    email,\n    password,\n    options: {\n      data: {\n        full_name: fullName,\n      },\n    },\n  })\n\n  if (error) throw error\n  return data\n}\n\nexport async function signIn(email: string, password: string) {\n  const { data, error } = await supabase.auth.signInWithPassword({\n    email,\n    password,\n  })\n\n  if (error) throw error\n  return data\n}\n\nexport async function signOut() {\n  const { error } = await supabase.auth.signOut()\n  if (error) throw error\n}\n\nexport async function getCurrentUser(): Promise<User | null> {\n  const { data: { user } } = await supabase.auth.getUser()\n  return user\n}\n\nexport async function getUserProfile(userId: string): Promise<UserProfile | null> {\n  const { data, error } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', userId)\n    .single()\n\n  if (error) {\n    console.error('Error fetching user profile:', error)\n    return null\n  }\n\n  return data\n}\n\nexport async function updateUserProfile(userId: string, updates: Partial<UserProfile>) {\n  const { data, error } = await supabase\n    .from('profiles')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single()\n\n  if (error) throw error\n  return data\n}\n\nexport async function isAdmin(userId: string): Promise<boolean> {\n  const profile = await getUserProfile(userId)\n  return profile?.role === 'admin'\n}\n\nexport async function resetPassword(email: string) {\n  const { error } = await supabase.auth.resetPasswordForEmail(email, {\n    redirectTo: `${window.location.origin}/reset-password`,\n  })\n\n  if (error) throw error\n}\n\nexport async function updatePassword(newPassword: string) {\n  const { error } = await supabase.auth.updateUser({\n    password: newPassword\n  })\n\n  if (error) throw error\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAaO,eAAe,OAAO,KAAa,EAAE,QAAgB,EAAE,QAAgB;IAC5E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;QACjD;QACA;QACA,SAAS;YACP,MAAM;gBACJ,WAAW;YACb;QACF;IACF;IAEA,IAAI,OAAO,MAAM;IACjB,OAAO;AACT;AAEO,eAAe,OAAO,KAAa,EAAE,QAAgB;IAC1D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;QAC7D;QACA;IACF;IAEA,IAAI,OAAO,MAAM;IACjB,OAAO;AACT;AAEO,eAAe;IACpB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAC7C,IAAI,OAAO,MAAM;AACnB;AAEO,eAAe;IACpB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IACtD,OAAO;AACT;AAEO,eAAe,eAAe,MAAc;IACjD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;IAEA,OAAO;AACT;AAEO,eAAe,kBAAkB,MAAc,EAAE,OAA6B;IACnF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO,MAAM;IACjB,OAAO;AACT;AAEO,eAAe,QAAQ,MAAc;IAC1C,MAAM,UAAU,MAAM,eAAe;IACrC,OAAO,SAAS,SAAS;AAC3B;AAEO,eAAe,cAAc,KAAa;IAC/C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;QACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC;IACxD;IAEA,IAAI,OAAO,MAAM;AACnB;AAEO,eAAe,eAAe,WAAmB;IACtD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;QAC/C,UAAU;IACZ;IAEA,IAAI,OAAO,MAAM;AACnB", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/mca/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\nimport { UserProfile, getUserProfile } from '@/lib/auth'\n\ninterface AuthContextType {\n  user: User | null\n  profile: UserProfile | null\n  loading: boolean\n  signOut: () => Promise<void>\n  refreshProfile: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<UserProfile | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  const refreshProfile = async () => {\n    if (user) {\n      const userProfile = await getUserProfile(user.id)\n      setProfile(userProfile)\n    }\n  }\n\n  const handleSignOut = async () => {\n    await supabase.auth.signOut()\n    setUser(null)\n    setProfile(null)\n  }\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      setUser(session?.user ?? null)\n      \n      if (session?.user) {\n        const userProfile = await getUserProfile(session.user.id)\n        setProfile(userProfile)\n      }\n      \n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        \n        if (session?.user) {\n          const userProfile = await getUserProfile(session.user.id)\n          setProfile(userProfile)\n        } else {\n          setProfile(null)\n        }\n        \n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const value = {\n    user,\n    profile,\n    loading,\n    signOut: handleSignOut,\n    refreshProfile,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AALA;;;;;AAeA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,iBAAiB;QACrB,IAAI,MAAM;YACR,MAAM,cAAc,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,EAAE;YAChD,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAC3B,QAAQ;QACR,WAAW;IACb;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sBAAsB;QACtB,MAAM,oBAAoB;YACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAC5D,QAAQ,SAAS,QAAQ;YAEzB,IAAI,SAAS,MAAM;gBACjB,MAAM,cAAc,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI,CAAC,EAAE;gBACxD,WAAW;YACb;YAEA,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,QAAQ,SAAS,QAAQ;YAEzB,IAAI,SAAS,MAAM;gBACjB,MAAM,cAAc,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI,CAAC,EAAE;gBACxD,WAAW;YACb,OAAO;gBACL,WAAW;YACb;YAEA,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,QAAQ;QACZ;QACA;QACA;QACA,SAAS;QACT;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}