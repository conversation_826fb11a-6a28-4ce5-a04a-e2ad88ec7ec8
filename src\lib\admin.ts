import { supabase } from './supabase'
import { Document, DocumentWithProfile } from './documents'
import { UserProfile } from './auth'

export interface AdminStats {
  totalUsers: number
  totalDocuments: number
  pendingDocuments: number
  totalDownloads: number
  totalComments: number
  totalRatings: number
}

export async function getAdminStats(): Promise<AdminStats> {
  const [
    usersResult,
    documentsResult,
    pendingResult,
    downloadsResult,
    commentsResult,
    ratingsResult
  ] = await Promise.all([
    supabase.from('profiles').select('id', { count: 'exact', head: true }),
    supabase.from('documents').select('id', { count: 'exact', head: true }),
    supabase.from('documents').select('id', { count: 'exact', head: true }).eq('is_approved', false),
    supabase.from('documents').select('download_count'),
    supabase.from('comments').select('id', { count: 'exact', head: true }),
    supabase.from('ratings').select('id', { count: 'exact', head: true })
  ])

  const totalDownloads = downloadsResult.data?.reduce((sum, doc) => sum + (doc.download_count || 0), 0) || 0

  return {
    totalUsers: usersResult.count || 0,
    totalDocuments: documentsResult.count || 0,
    pendingDocuments: pendingResult.count || 0,
    totalDownloads,
    totalComments: commentsResult.count || 0,
    totalRatings: ratingsResult.count || 0
  }
}

export async function getAllUsers(
  page: number = 1,
  limit: number = 20,
  searchTerm?: string
): Promise<{ users: UserProfile[], total: number }> {
  let query = supabase
    .from('profiles')
    .select('*', { count: 'exact' })
    .order('created_at', { ascending: false })

  if (searchTerm) {
    query = query.or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`)
  }

  const from = (page - 1) * limit
  const to = from + limit - 1
  query = query.range(from, to)

  const { data, error, count } = await query

  if (error) throw error

  return {
    users: data || [],
    total: count || 0
  }
}

export async function updateUserRole(userId: string, role: 'user' | 'admin'): Promise<UserProfile> {
  const { data, error } = await supabase
    .from('profiles')
    .update({ role })
    .eq('id', userId)
    .select()
    .single()

  if (error) throw error
  return data
}

export async function deleteUser(userId: string): Promise<void> {
  // Note: This will cascade delete all user's documents, comments, and ratings
  // due to foreign key constraints
  const { error } = await supabase
    .from('profiles')
    .delete()
    .eq('id', userId)

  if (error) throw error
}

export async function getPendingDocuments(
  page: number = 1,
  limit: number = 20
): Promise<{ documents: DocumentWithProfile[], total: number }> {
  const from = (page - 1) * limit
  const to = from + limit - 1

  const { data, error, count } = await supabase
    .from('documents')
    .select(`
      *,
      profiles:upload_user_id (
        full_name,
        email
      )
    `, { count: 'exact' })
    .eq('is_approved', false)
    .order('created_at', { ascending: false })
    .range(from, to)

  if (error) throw error

  return {
    documents: data || [],
    total: count || 0
  }
}

export async function getAllDocuments(
  page: number = 1,
  limit: number = 20,
  searchTerm?: string,
  filterApproved?: boolean
): Promise<{ documents: DocumentWithProfile[], total: number }> {
  let query = supabase
    .from('documents')
    .select(`
      *,
      profiles:upload_user_id (
        full_name,
        email
      )
    `, { count: 'exact' })
    .order('created_at', { ascending: false })

  if (searchTerm) {
    query = query.textSearch('title,description,subject,author', searchTerm)
  }

  if (filterApproved !== undefined) {
    query = query.eq('is_approved', filterApproved)
  }

  const from = (page - 1) * limit
  const to = from + limit - 1
  query = query.range(from, to)

  const { data, error, count } = await query

  if (error) throw error

  return {
    documents: data || [],
    total: count || 0
  }
}

export async function approveDocument(documentId: string): Promise<Document> {
  const { data, error } = await supabase
    .from('documents')
    .update({ is_approved: true })
    .eq('id', documentId)
    .select()
    .single()

  if (error) throw error
  return data
}

export async function rejectDocument(documentId: string): Promise<void> {
  // Get document info first to delete the file
  const { data: document, error: fetchError } = await supabase
    .from('documents')
    .select('file_url')
    .eq('id', documentId)
    .single()

  if (fetchError) throw fetchError

  // Extract file path from URL and delete from storage
  const url = new URL(document.file_url)
  const filePath = url.pathname.split('/').slice(-2).join('/')

  const { error: storageError } = await supabase.storage
    .from('documents')
    .remove([filePath])

  if (storageError) {
    console.error('Error deleting file from storage:', storageError)
  }

  // Delete document record
  const { error: deleteError } = await supabase
    .from('documents')
    .delete()
    .eq('id', documentId)

  if (deleteError) throw deleteError
}

export async function deleteDocumentAsAdmin(documentId: string): Promise<void> {
  // Get document info first to delete the file
  const { data: document, error: fetchError } = await supabase
    .from('documents')
    .select('file_url')
    .eq('id', documentId)
    .single()

  if (fetchError) throw fetchError

  // Extract file path from URL and delete from storage
  const url = new URL(document.file_url)
  const filePath = url.pathname.split('/').slice(-2).join('/')

  const { error: storageError } = await supabase.storage
    .from('documents')
    .remove([filePath])

  if (storageError) {
    console.error('Error deleting file from storage:', storageError)
  }

  // Delete document record
  const { error: deleteError } = await supabase
    .from('documents')
    .delete()
    .eq('id', documentId)

  if (deleteError) throw deleteError
}

export async function getRecentActivity(limit: number = 10): Promise<{
  recentDocuments: DocumentWithProfile[]
  recentUsers: UserProfile[]
}> {
  const [documentsResult, usersResult] = await Promise.all([
    supabase
      .from('documents')
      .select(`
        *,
        profiles:upload_user_id (
          full_name,
          email
        )
      `)
      .order('created_at', { ascending: false })
      .limit(limit),
    supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit)
  ])

  if (documentsResult.error) throw documentsResult.error
  if (usersResult.error) throw usersResult.error

  return {
    recentDocuments: documentsResult.data || [],
    recentUsers: usersResult.data || []
  }
}
