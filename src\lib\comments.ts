import { supabase } from './supabase'

export interface Comment {
  id: string
  document_id: string
  user_id: string
  content: string
  parent_id: string | null
  created_at: string
  updated_at: string
}

export interface CommentWithProfile extends Comment {
  profiles: {
    full_name: string | null
    email: string
    avatar_url: string | null
  }
  replies?: CommentWithProfile[]
}

export interface Rating {
  id: string
  document_id: string
  user_id: string
  rating: number
  created_at: string
  updated_at: string
}

export async function getDocumentComments(documentId: string): Promise<CommentWithProfile[]> {
  const { data, error } = await supabase
    .from('comments')
    .select(`
      *,
      profiles:user_id (
        full_name,
        email,
        avatar_url
      )
    `)
    .eq('document_id', documentId)
    .order('created_at', { ascending: true })

  if (error) throw error

  // Organize comments into a tree structure
  const commentsMap = new Map<string, CommentWithProfile>()
  const rootComments: CommentWithProfile[] = []

  // First pass: create all comment objects
  data.forEach(comment => {
    const commentWithReplies: CommentWithProfile = {
      ...comment,
      replies: []
    }
    commentsMap.set(comment.id, commentWithReplies)
  })

  // Second pass: organize into tree structure
  data.forEach(comment => {
    const commentObj = commentsMap.get(comment.id)!
    
    if (comment.parent_id) {
      const parent = commentsMap.get(comment.parent_id)
      if (parent) {
        parent.replies!.push(commentObj)
      }
    } else {
      rootComments.push(commentObj)
    }
  })

  return rootComments
}

export async function addComment(
  documentId: string,
  userId: string,
  content: string,
  parentId?: string
): Promise<CommentWithProfile> {
  const { data, error } = await supabase
    .from('comments')
    .insert({
      document_id: documentId,
      user_id: userId,
      content,
      parent_id: parentId || null
    })
    .select(`
      *,
      profiles:user_id (
        full_name,
        email,
        avatar_url
      )
    `)
    .single()

  if (error) throw error
  return { ...data, replies: [] }
}

export async function updateComment(
  commentId: string,
  userId: string,
  content: string
): Promise<CommentWithProfile> {
  const { data, error } = await supabase
    .from('comments')
    .update({ content })
    .eq('id', commentId)
    .eq('user_id', userId)
    .select(`
      *,
      profiles:user_id (
        full_name,
        email,
        avatar_url
      )
    `)
    .single()

  if (error) throw error
  return { ...data, replies: [] }
}

export async function deleteComment(commentId: string, userId: string): Promise<void> {
  const { error } = await supabase
    .from('comments')
    .delete()
    .eq('id', commentId)
    .eq('user_id', userId)

  if (error) throw error
}

export async function rateDocument(
  documentId: string,
  userId: string,
  rating: number
): Promise<Rating> {
  const { data, error } = await supabase
    .from('ratings')
    .upsert({
      document_id: documentId,
      user_id: userId,
      rating
    })
    .select()
    .single()

  if (error) throw error
  return data
}

export async function getDocumentRating(documentId: string): Promise<{
  averageRating: number
  totalRatings: number
}> {
  const { data, error } = await supabase
    .from('ratings')
    .select('rating')
    .eq('document_id', documentId)

  if (error) throw error

  if (!data || data.length === 0) {
    return { averageRating: 0, totalRatings: 0 }
  }

  const totalRatings = data.length
  const sum = data.reduce((acc, curr) => acc + curr.rating, 0)
  const averageRating = sum / totalRatings

  return {
    averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
    totalRatings
  }
}

export async function getUserRating(
  documentId: string,
  userId: string
): Promise<number | null> {
  const { data, error } = await supabase
    .from('ratings')
    .select('rating')
    .eq('document_id', documentId)
    .eq('user_id', userId)
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      // No rating found
      return null
    }
    throw error
  }

  return data.rating
}

export async function deleteRating(documentId: string, userId: string): Promise<void> {
  const { error } = await supabase
    .from('ratings')
    .delete()
    .eq('document_id', documentId)
    .eq('user_id', userId)

  if (error) throw error
}
