{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/mca/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient, createServerClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\nif (!supabaseUrl || !supabaseAnonKey) {\n  throw new Error('Missing Supabase environment variables. Please check your .env.local file.')\n}\n\n// Client-side Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Server-side Supabase client with service role key\nexport const supabaseAdmin = createClient(\n  supabaseUrl,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!,\n  {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  }\n)\n\n// Server-side client factory for SSR\nexport const createSupabaseServerClient = (cookieStore: any) => {\n  return createServerClient(\n    supabaseUrl,\n    supabase<PERSON><PERSON><PERSON><PERSON>,\n    {\n      cookies: {\n        get(name: string) {\n          return cookieStore.get(name)?.value\n        },\n        set(name: string, value: string, options: any) {\n          cookieStore.set({ name, value, ...options })\n        },\n        remove(name: string, options: any) {\n          cookieStore.set({ name, value: '', ...options })\n        },\n      },\n    }\n  )\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string | null\n          avatar_url: string | null\n          role: 'user' | 'admin'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name?: string | null\n          avatar_url?: string | null\n          role?: 'user' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          full_name?: string | null\n          avatar_url?: string | null\n          role?: 'user' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      documents: {\n        Row: {\n          id: string\n          title: string\n          description: string | null\n          subject: string\n          author: string | null\n          file_url: string\n          file_name: string\n          file_size: number\n          file_type: string\n          thumbnail_url: string | null\n          upload_user_id: string\n          download_count: number\n          is_approved: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          title: string\n          description?: string | null\n          subject: string\n          author?: string | null\n          file_url: string\n          file_name: string\n          file_size: number\n          file_type: string\n          thumbnail_url?: string | null\n          upload_user_id: string\n          download_count?: number\n          is_approved?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          title?: string\n          description?: string | null\n          subject?: string\n          author?: string | null\n          file_url?: string\n          file_name?: string\n          file_size?: number\n          file_type?: string\n          thumbnail_url?: string | null\n          upload_user_id?: string\n          download_count?: number\n          is_approved?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      comments: {\n        Row: {\n          id: string\n          document_id: string\n          user_id: string\n          content: string\n          parent_id: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          document_id: string\n          user_id: string\n          content: string\n          parent_id?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          document_id?: string\n          user_id?: string\n          content?: string\n          parent_id?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      ratings: {\n        Row: {\n          id: string\n          document_id: string\n          user_id: string\n          rating: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          document_id: string\n          user_id: string\n          rating: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          document_id?: string\n          user_id?: string\n          rating?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGoB;AAHpB;AACA;AAAA;;;AAEA,MAAM;AACN,MAAM;AAEN;;AAKO,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EACtC,aACA,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB,EACrC;IACE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;AACF;AAIK,MAAM,6BAA6B,CAAC;IACzC,OAAO,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;QACE,SAAS;YACP,KAAI,IAAY;oBACP;gBAAP,QAAO,mBAAA,YAAY,GAAG,CAAC,mBAAhB,uCAAA,iBAAuB,KAAK;YACrC;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAY;gBAC3C,YAAY,GAAG,CAAC;oBAAE;oBAAM;oBAAO,GAAG,OAAO;gBAAC;YAC5C;YACA,QAAO,IAAY,EAAE,OAAY;gBAC/B,YAAY,GAAG,CAAC;oBAAE;oBAAM,OAAO;oBAAI,GAAG,OAAO;gBAAC;YAChD;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/mca/src/lib/auth.ts"], "sourcesContent": ["import { supabase } from './supabase'\nimport { User } from '@supabase/supabase-js'\n\nexport interface UserProfile {\n  id: string\n  email: string\n  full_name: string | null\n  avatar_url: string | null\n  role: 'user' | 'admin'\n  created_at: string\n  updated_at: string\n}\n\nexport async function signUp(email: string, password: string, fullName: string) {\n  const { data, error } = await supabase.auth.signUp({\n    email,\n    password,\n    options: {\n      data: {\n        full_name: fullName,\n      },\n    },\n  })\n\n  if (error) throw error\n  return data\n}\n\nexport async function signIn(email: string, password: string) {\n  const { data, error } = await supabase.auth.signInWithPassword({\n    email,\n    password,\n  })\n\n  if (error) throw error\n  return data\n}\n\nexport async function signOut() {\n  const { error } = await supabase.auth.signOut()\n  if (error) throw error\n}\n\nexport async function getCurrentUser(): Promise<User | null> {\n  const { data: { user } } = await supabase.auth.getUser()\n  return user\n}\n\nexport async function getUserProfile(userId: string): Promise<UserProfile | null> {\n  const { data, error } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', userId)\n    .single()\n\n  if (error) {\n    console.error('Error fetching user profile:', error)\n    return null\n  }\n\n  return data\n}\n\nexport async function updateUserProfile(userId: string, updates: Partial<UserProfile>) {\n  const { data, error } = await supabase\n    .from('profiles')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single()\n\n  if (error) throw error\n  return data\n}\n\nexport async function isAdmin(userId: string): Promise<boolean> {\n  const profile = await getUserProfile(userId)\n  return profile?.role === 'admin'\n}\n\nexport async function resetPassword(email: string) {\n  const { error } = await supabase.auth.resetPasswordForEmail(email, {\n    redirectTo: `${window.location.origin}/reset-password`,\n  })\n\n  if (error) throw error\n}\n\nexport async function updatePassword(newPassword: string) {\n  const { error } = await supabase.auth.updateUser({\n    password: newPassword\n  })\n\n  if (error) throw error\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAaO,eAAe,OAAO,KAAa,EAAE,QAAgB,EAAE,QAAgB;IAC5E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;QACjD;QACA;QACA,SAAS;YACP,MAAM;gBACJ,WAAW;YACb;QACF;IACF;IAEA,IAAI,OAAO,MAAM;IACjB,OAAO;AACT;AAEO,eAAe,OAAO,KAAa,EAAE,QAAgB;IAC1D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;QAC7D;QACA;IACF;IAEA,IAAI,OAAO,MAAM;IACjB,OAAO;AACT;AAEO,eAAe;IACpB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAC7C,IAAI,OAAO,MAAM;AACnB;AAEO,eAAe;IACpB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IACtD,OAAO;AACT;AAEO,eAAe,eAAe,MAAc;IACjD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;IAEA,OAAO;AACT;AAEO,eAAe,kBAAkB,MAAc,EAAE,OAA6B;IACnF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO,MAAM;IACjB,OAAO;AACT;AAEO,eAAe,QAAQ,MAAc;IAC1C,MAAM,UAAU,MAAM,eAAe;IACrC,OAAO,CAAA,oBAAA,8BAAA,QAAS,IAAI,MAAK;AAC3B;AAEO,eAAe,cAAc,KAAa;IAC/C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;QACjE,YAAY,AAAC,GAAyB,OAAvB,OAAO,QAAQ,CAAC,MAAM,EAAC;IACxC;IAEA,IAAI,OAAO,MAAM;AACnB;AAEO,eAAe,eAAe,WAAmB;IACtD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;QAC/C,UAAU;IACZ;IAEA,IAAI,OAAO,MAAM;AACnB", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/mca/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\nimport { UserProfile, getUserProfile } from '@/lib/auth'\n\ninterface AuthContextType {\n  user: User | null\n  profile: UserProfile | null\n  loading: boolean\n  signOut: () => Promise<void>\n  refreshProfile: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<UserProfile | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  const refreshProfile = async () => {\n    if (user) {\n      const userProfile = await getUserProfile(user.id)\n      setProfile(userProfile)\n    }\n  }\n\n  const handleSignOut = async () => {\n    await supabase.auth.signOut()\n    setUser(null)\n    setProfile(null)\n  }\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      setUser(session?.user ?? null)\n      \n      if (session?.user) {\n        const userProfile = await getUserProfile(session.user.id)\n        setProfile(userProfile)\n      }\n      \n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        \n        if (session?.user) {\n          const userProfile = await getUserProfile(session.user.id)\n          setProfile(userProfile)\n        } else {\n          setProfile(null)\n        }\n        \n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const value = {\n    user,\n    profile,\n    loading,\n    signOut: handleSignOut,\n    refreshProfile,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;;;AALA;;;;AAeA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,iBAAiB;QACrB,IAAI,MAAM;YACR,MAAM,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,EAAE;YAChD,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAC3B,QAAQ;QACR,WAAW;IACb;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,MAAM;4DAAoB;oBACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;wBACpD;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBAEzB,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;wBACjB,MAAM,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI,CAAC,EAAE;wBACxD,WAAW;oBACb;oBAEA,WAAW;gBACb;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;wBACJ;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBAEzB,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;wBACjB,MAAM,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI,CAAC,EAAE;wBACxD,WAAW;oBACb,OAAO;wBACL,WAAW;oBACb;oBAEA,WAAW;gBACb;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,QAAQ;QACZ;QACA;QACA;QACA,SAAS;QACT;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAlEgB;KAAA;AAoET,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}