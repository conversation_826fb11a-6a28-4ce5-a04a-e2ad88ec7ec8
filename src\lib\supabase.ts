import { createClient } from '@supabase/supabase-js'
import { createBrowserClient, createServerClient } from '@supabase/ssr'

// Get environment variables with fallbacks
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://vpqtxnsfngpdksdyzyfz.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZwcXR4bnNmbmdwZGtzZHl6eWZ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQxMDkxMTMsImV4cCI6MjA2OTY4NTExM30._OFn3uxbbdtcYIdYFmGc7sEYVXAutHTC4NhQ_54YsTc'

// Debug logging
if (typeof window !== 'undefined') {
  console.log('Client-side environment check:', {
    supabaseUrl: supabaseUrl ? 'Set' : 'Missing',
    supabaseKey: supabaseAnonKey ? 'Set' : 'Missing',
    urlLength: supabaseUrl?.length,
    keyLength: supabase<PERSON><PERSON><PERSON>ey?.length
  })
}

// Client-side Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Server-side Supabase client with service role key
export const supabaseAdmin = createClient(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Server-side client factory for SSR
export const createSupabaseServerClient = (cookieStore: any) => {
  return createServerClient(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          cookieStore.set({ name, value, ...options })
        },
        remove(name: string, options: any) {
          cookieStore.set({ name, value: '', ...options })
        },
      },
    }
  )
}

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          role: 'user' | 'admin'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'user' | 'admin'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'user' | 'admin'
          created_at?: string
          updated_at?: string
        }
      }
      documents: {
        Row: {
          id: string
          title: string
          description: string | null
          subject: string
          author: string | null
          file_url: string
          file_name: string
          file_size: number
          file_type: string
          thumbnail_url: string | null
          upload_user_id: string
          download_count: number
          is_approved: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          subject: string
          author?: string | null
          file_url: string
          file_name: string
          file_size: number
          file_type: string
          thumbnail_url?: string | null
          upload_user_id: string
          download_count?: number
          is_approved?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          subject?: string
          author?: string | null
          file_url?: string
          file_name?: string
          file_size?: number
          file_type?: string
          thumbnail_url?: string | null
          upload_user_id?: string
          download_count?: number
          is_approved?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      comments: {
        Row: {
          id: string
          document_id: string
          user_id: string
          content: string
          parent_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          document_id: string
          user_id: string
          content: string
          parent_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          document_id?: string
          user_id?: string
          content?: string
          parent_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      ratings: {
        Row: {
          id: string
          document_id: string
          user_id: string
          rating: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          document_id: string
          user_id: string
          rating: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          document_id?: string
          user_id?: string
          rating?: number
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}
