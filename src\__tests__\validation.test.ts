import {
  signUpSchema,
  signInSchema,
  uploadDocumentSchema,
  addCommentSchema,
  rateDocumentSchema,
  validateFile,
  sanitizeInput,
  sanitizeSearchQuery,
} from '@/lib/validation'

describe('Validation Schemas', () => {
  describe('signUpSchema', () => {
    it('should validate correct sign up data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'Password123',
        fullName: '<PERSON>'
      }
      
      const result = signUpSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('should reject invalid email', () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'Password123',
        fullName: '<PERSON>'
      }
      
      const result = signUpSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })

    it('should reject weak password', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'weak',
        fullName: '<PERSON>'
      }
      
      const result = signUpSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })

    it('should reject invalid full name', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'Password123',
        fullName: 'J'
      }
      
      const result = signUpSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })
  })

  describe('signInSchema', () => {
    it('should validate correct sign in data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'password'
      }
      
      const result = signInSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('should reject empty password', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: ''
      }
      
      const result = signInSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })
  })

  describe('uploadDocumentSchema', () => {
    it('should validate correct document data', () => {
      const validData = {
        title: 'Test Document',
        description: 'A test document',
        subject: 'Computer Science',
        author: 'John Doe'
      }
      
      const result = uploadDocumentSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('should reject short title', () => {
      const invalidData = {
        title: 'Te',
        description: 'A test document',
        subject: 'Computer Science',
        author: 'John Doe'
      }
      
      const result = uploadDocumentSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })

    it('should reject long title', () => {
      const invalidData = {
        title: 'A'.repeat(201),
        description: 'A test document',
        subject: 'Computer Science',
        author: 'John Doe'
      }
      
      const result = uploadDocumentSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })

    it('should allow empty optional fields', () => {
      const validData = {
        title: 'Test Document',
        description: '',
        subject: 'Computer Science',
        author: ''
      }
      
      const result = uploadDocumentSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })
  })

  describe('addCommentSchema', () => {
    it('should validate correct comment data', () => {
      const validData = {
        content: 'This is a test comment',
        document_id: '123e4567-e89b-12d3-a456-************'
      }
      
      const result = addCommentSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('should reject empty content', () => {
      const invalidData = {
        content: '',
        document_id: '123e4567-e89b-12d3-a456-************'
      }
      
      const result = addCommentSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })

    it('should reject invalid UUID', () => {
      const invalidData = {
        content: 'This is a test comment',
        document_id: 'invalid-uuid'
      }
      
      const result = addCommentSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })
  })

  describe('rateDocumentSchema', () => {
    it('should validate correct rating data', () => {
      const validData = {
        document_id: '123e4567-e89b-12d3-a456-************',
        rating: 4
      }
      
      const result = rateDocumentSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('should reject rating below 1', () => {
      const invalidData = {
        document_id: '123e4567-e89b-12d3-a456-************',
        rating: 0
      }
      
      const result = rateDocumentSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })

    it('should reject rating above 5', () => {
      const invalidData = {
        document_id: '123e4567-e89b-12d3-a456-************',
        rating: 6
      }
      
      const result = rateDocumentSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })
  })
})

describe('Validation Utilities', () => {
  describe('validateFile', () => {
    const createMockFile = (name: string, size: number, type: string) => {
      return { name, size, type } as File
    }

    it('should validate correct PDF file', () => {
      const file = createMockFile('test.pdf', 1000000, 'application/pdf')
      const result = validateFile(file)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should reject file that is too large', () => {
      const file = createMockFile('large.pdf', 60000000, 'application/pdf')
      const result = validateFile(file)
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })

    it('should reject unsupported file type', () => {
      const file = createMockFile('test.exe', 1000, 'application/exe')
      const result = validateFile(file)
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })
  })

  describe('sanitizeInput', () => {
    it('should remove HTML tags', () => {
      const input = '<script>alert("xss")</script>Hello World'
      const result = sanitizeInput(input)
      expect(result).toBe('alert("xss")Hello World')
    })

    it('should remove javascript protocols', () => {
      const input = 'javascript:alert("xss")'
      const result = sanitizeInput(input)
      expect(result).toBe('alert("xss")')
    })

    it('should remove event handlers', () => {
      const input = 'onclick=alert("xss") Hello'
      const result = sanitizeInput(input)
      expect(result).toBe('alert("xss") Hello')
    })

    it('should trim whitespace', () => {
      const input = '  Hello World  '
      const result = sanitizeInput(input)
      expect(result).toBe('Hello World')
    })
  })

  describe('sanitizeSearchQuery', () => {
    it('should allow alphanumeric characters and spaces', () => {
      const query = 'Hello World 123'
      const result = sanitizeSearchQuery(query)
      expect(result).toBe('Hello World 123')
    })

    it('should remove special characters', () => {
      const query = 'Hello@#$%^&*()World'
      const result = sanitizeSearchQuery(query)
      expect(result).toBe('HelloWorld')
    })

    it('should allow hyphens', () => {
      const query = 'Hello-World'
      const result = sanitizeSearchQuery(query)
      expect(result).toBe('Hello-World')
    })

    it('should limit length to 200 characters', () => {
      const query = 'A'.repeat(250)
      const result = sanitizeSearchQuery(query)
      expect(result.length).toBe(200)
    })

    it('should trim whitespace', () => {
      const query = '  Hello World  '
      const result = sanitizeSearchQuery(query)
      expect(result).toBe('Hello World')
    })
  })
})
