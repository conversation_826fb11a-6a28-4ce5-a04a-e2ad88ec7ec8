{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/mca/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { useRouter, usePathname } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function Navigation() {\n  const { user, profile, signOut } = useAuth()\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const router = useRouter()\n  const pathname = usePathname()\n\n  const handleSignOut = async () => {\n    await signOut()\n    router.push('/')\n  }\n\n  const isActive = (path: string) => pathname === path\n\n  const navigation = [\n    { name: 'Browse', href: '/browse', icon: '🔍' },\n    { name: 'Upload', href: '/upload', icon: '📤' },\n    { name: 'Dashboard', href: '/dashboard', icon: '📊' },\n  ]\n\n  const adminNavigation = [\n    { name: 'Admin Panel', href: '/admin', icon: '⚙️' },\n  ]\n\n  return (\n    <nav className=\"bg-white shadow-lg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0 flex items-center\">\n              <Link href=\"/\" className=\"text-xl font-bold text-indigo-600\">\n                📚 Digital Library\n              </Link>\n            </div>\n            \n            {user && (\n              <div className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`${\n                      isActive(item.href)\n                        ? 'border-indigo-500 text-gray-900'\n                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'\n                    } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}\n                  >\n                    <span className=\"mr-2\">{item.icon}</span>\n                    {item.name}\n                  </Link>\n                ))}\n                \n                {profile?.role === 'admin' && adminNavigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`${\n                      isActive(item.href)\n                        ? 'border-indigo-500 text-gray-900'\n                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'\n                    } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}\n                  >\n                    <span className=\"mr-2\">{item.icon}</span>\n                    {item.name}\n                  </Link>\n                ))}\n              </div>\n            )}\n          </div>\n          \n          <div className=\"hidden sm:ml-6 sm:flex sm:items-center\">\n            {user ? (\n              <div className=\"ml-3 relative\">\n                <div className=\"flex items-center space-x-4\">\n                  <span className=\"text-sm text-gray-700\">\n                    Welcome, {profile?.full_name || user.email}\n                  </span>\n                  <div className=\"relative\">\n                    <button\n                      onClick={() => setIsMenuOpen(!isMenuOpen)}\n                      className=\"bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n                    >\n                      <span className=\"sr-only\">Open user menu</span>\n                      <div className=\"h-8 w-8 rounded-full bg-indigo-600 flex items-center justify-center text-white text-sm font-medium\">\n                        {profile?.full_name?.charAt(0) || user.email?.charAt(0) || 'U'}\n                      </div>\n                    </button>\n                    \n                    {isMenuOpen && (\n                      <div className=\"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50\">\n                        <div className=\"py-1\">\n                          <Link\n                            href=\"/profile\"\n                            className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                            onClick={() => setIsMenuOpen(false)}\n                          >\n                            👤 Your Profile\n                          </Link>\n                          <Link\n                            href=\"/dashboard\"\n                            className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                            onClick={() => setIsMenuOpen(false)}\n                          >\n                            📊 Dashboard\n                          </Link>\n                          <button\n                            onClick={handleSignOut}\n                            className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                          >\n                            🚪 Sign out\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Sign in\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Sign up\n                </Link>\n              </div>\n            )}\n          </div>\n          \n          {/* Mobile menu button */}\n          <div className=\"sm:hidden flex items-center\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              <svg\n                className={`${isMenuOpen ? 'hidden' : 'block'} h-6 w-6`}\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n              <svg\n                className={`${isMenuOpen ? 'block' : 'hidden'} h-6 w-6`}\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n      \n      {/* Mobile menu */}\n      {isMenuOpen && (\n        <div className=\"sm:hidden\">\n          <div className=\"pt-2 pb-3 space-y-1\">\n            {user ? (\n              <>\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`${\n                      isActive(item.href)\n                        ? 'bg-indigo-50 border-indigo-500 text-indigo-700'\n                        : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700'\n                    } block pl-3 pr-4 py-2 border-l-4 text-base font-medium`}\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    <span className=\"mr-2\">{item.icon}</span>\n                    {item.name}\n                  </Link>\n                ))}\n                \n                {profile?.role === 'admin' && adminNavigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`${\n                      isActive(item.href)\n                        ? 'bg-indigo-50 border-indigo-500 text-indigo-700'\n                        : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700'\n                    } block pl-3 pr-4 py-2 border-l-4 text-base font-medium`}\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    <span className=\"mr-2\">{item.icon}</span>\n                    {item.name}\n                  </Link>\n                ))}\n                \n                <div className=\"border-t border-gray-200 pt-4 pb-3\">\n                  <div className=\"flex items-center px-4\">\n                    <div className=\"flex-shrink-0\">\n                      <div className=\"h-10 w-10 rounded-full bg-indigo-600 flex items-center justify-center text-white font-medium\">\n                        {profile?.full_name?.charAt(0) || user.email?.charAt(0) || 'U'}\n                      </div>\n                    </div>\n                    <div className=\"ml-3\">\n                      <div className=\"text-base font-medium text-gray-800\">\n                        {profile?.full_name || user.email}\n                      </div>\n                      <div className=\"text-sm font-medium text-gray-500\">\n                        {user.email}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"mt-3 space-y-1\">\n                    <Link\n                      href=\"/profile\"\n                      className=\"block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100\"\n                      onClick={() => setIsMenuOpen(false)}\n                    >\n                      👤 Your Profile\n                    </Link>\n                    <button\n                      onClick={handleSignOut}\n                      className=\"block w-full text-left px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100\"\n                    >\n                      🚪 Sign out\n                    </button>\n                  </div>\n                </div>\n              </>\n            ) : (\n              <div className=\"space-y-1\">\n                <Link\n                  href=\"/auth/login\"\n                  className=\"block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  Sign in\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  Sign up\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,gBAAgB;QACpB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,WAAW,CAAC,OAAiB,aAAa;IAEhD,MAAM,aAAa;QACjB;YAAE,MAAM;YAAU,MAAM;YAAW,MAAM;QAAK;QAC9C;YAAE,MAAM;YAAU,MAAM;YAAW,MAAM;QAAK;QAC9C;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM;QAAK;KACrD;IAED,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAe,MAAM;YAAU,MAAM;QAAK;KACnD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAoC;;;;;;;;;;;gCAK9D,sBACC,8OAAC;oCAAI,WAAU;;wCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAW,GACT,SAAS,KAAK,IAAI,IACd,oCACA,6EACL,kEAAkE,CAAC;;kEAEpE,8OAAC;wDAAK,WAAU;kEAAQ,KAAK,IAAI;;;;;;oDAChC,KAAK,IAAI;;+CATL,KAAK,IAAI;;;;;wCAajB,SAAS,SAAS,WAAW,gBAAgB,GAAG,CAAC,CAAC,qBACjD,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAW,GACT,SAAS,KAAK,IAAI,IACd,oCACA,6EACL,kEAAkE,CAAC;;kEAEpE,8OAAC;wDAAK,WAAU;kEAAQ,KAAK,IAAI;;;;;;oDAChC,KAAK,IAAI;;+CATL,KAAK,IAAI;;;;;;;;;;;;;;;;;sCAgBxB,8OAAC;4BAAI,WAAU;sCACZ,qBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;gDAAwB;gDAC5B,SAAS,aAAa,KAAK,KAAK;;;;;;;sDAE5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,cAAc,CAAC;oDAC9B,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC;4DAAI,WAAU;sEACZ,SAAS,WAAW,OAAO,MAAM,KAAK,KAAK,EAAE,OAAO,MAAM;;;;;;;;;;;;gDAI9D,4BACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,cAAc;0EAC9B;;;;;;0EAGD,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,cAAc;0EAC9B;;;;;;0EAGD,8OAAC;gEACC,SAAS;gEACT,WAAU;0EACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qDAUb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAQP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCACC,WAAW,GAAG,aAAa,WAAW,QAAQ,QAAQ,CAAC;wCACvD,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;kDAEP,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,8OAAC;wCACC,WAAW,GAAG,aAAa,UAAU,SAAS,QAAQ,CAAC;wCACvD,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;kDAEP,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9E,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,qBACC;;4BACG,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,GACT,SAAS,KAAK,IAAI,IACd,mDACA,8FACL,sDAAsD,CAAC;oCACxD,SAAS,IAAM,cAAc;;sDAE7B,8OAAC;4CAAK,WAAU;sDAAQ,KAAK,IAAI;;;;;;wCAChC,KAAK,IAAI;;mCAVL,KAAK,IAAI;;;;;4BAcjB,SAAS,SAAS,WAAW,gBAAgB,GAAG,CAAC,CAAC,qBACjD,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,GACT,SAAS,KAAK,IAAI,IACd,mDACA,8FACL,sDAAsD,CAAC;oCACxD,SAAS,IAAM,cAAc;;sDAE7B,8OAAC;4CAAK,WAAU;sDAAQ,KAAK,IAAI;;;;;;wCAChC,KAAK,IAAI;;mCAVL,KAAK,IAAI;;;;;0CAclB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,SAAS,WAAW,OAAO,MAAM,KAAK,KAAK,EAAE,OAAO,MAAM;;;;;;;;;;;0DAG/D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,SAAS,aAAa,KAAK,KAAK;;;;;;kEAEnC,8OAAC;wDAAI,WAAU;kEACZ,KAAK,KAAK;;;;;;;;;;;;;;;;;;kDAIjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;0DAC9B;;;;;;0DAGD,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;;qDAOP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}