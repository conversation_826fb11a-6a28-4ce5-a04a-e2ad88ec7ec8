{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_7b58270d._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ae544a93.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GrbJm9VpDetktIS6+XSVsIk+v59TT6wnI+IsWH88ls8=", "__NEXT_PREVIEW_MODE_ID": "484364dd8ba8488b938a1c9c0d592cc9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c7a119d1fd5913ba587de3247ec25f9db66c34f7044182cf75d44f5e2361e9f0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b9224d948a08d9a6b8529dc890b8aca8ed169a284855b3ee4a41a7b98ad56aa5"}}}, "instrumentation": null, "functions": {}}