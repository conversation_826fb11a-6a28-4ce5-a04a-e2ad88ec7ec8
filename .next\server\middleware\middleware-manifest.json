{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_7b58270d._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ae544a93.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GrbJm9VpDetktIS6+XSVsIk+v59TT6wnI+IsWH88ls8=", "__NEXT_PREVIEW_MODE_ID": "65a6a2bed8ba03066a4c73ffafbdabad", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d33ef6776e5281c323cf1473c70850df1b776f52dd2ad3d60d74eb8bbb64ecef", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d8952615f52d8371e67dabd49ab8f4a900b706171d6e3ebdbb5c850960b19739"}}}, "instrumentation": null, "functions": {}}