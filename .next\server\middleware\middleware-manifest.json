{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_7b58270d._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ae544a93.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GrbJm9VpDetktIS6+XSVsIk+v59TT6wnI+IsWH88ls8=", "__NEXT_PREVIEW_MODE_ID": "4bbe91a563059658838c05e41a2921cd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1a33bd88b005c5da99ee50794f801e773e505e7d6b8d0d1af734d0fbc31a86a0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c30ca77999f9950a5c69bfb947d1ace73348b5a310f4c73b4baf4144a9bbe0ad"}}}, "instrumentation": null, "functions": {}}